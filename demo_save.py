import matplotlib
# 设置后端，解决图像不显示的问题
matplotlib.use('Agg')  # 使用 Agg 后端保存图片
import matplotlib.pyplot as plt
import numpy as np

# 设置中文字体，解决中文显示问题
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']  # 支持中文字体
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# 生成数据
x = np.arange(0, 10, 0.1)  # x 轴：0 到 10，步长 0.1
y = np.sin(x)              # y 轴：正弦函数值

# 绘图
plt.figure(figsize=(10, 6))  # 设置图片大小
plt.plot(x, y, color='r', linestyle='-', linewidth=2, label='sin(x)')  # 红色折线，线宽 2
plt.title('正弦曲线示例')         # 标题
plt.xlabel('X 轴')               # X 轴标签
plt.ylabel('Y 轴')               # Y 轴标签
plt.legend()                     # 显示图例
plt.grid(linestyle='--', alpha=0.5)  # 网格线（虚线、透明度 0.5）

# 保存图片
plt.savefig('sine_curve.png', dpi=300, bbox_inches='tight')
print("图片已保存为 sine_curve.png")

# 也可以显示图片（如果支持的话）
try:
    plt.show()
except:
    print("无法显示图片窗口，但图片已保存")
