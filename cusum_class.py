import numpy as np 
import matplotlib
# 设置后端，解决图像不显示的问题
matplotlib.use('Agg')  # 使用 Agg 后端保存图片
import matplotlib.pyplot as plt
from matplotlib import rcParams
import os
import pandas as pd
import cv2

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

from AD_analysis import data_path
from AD_analysis import *

class CUSUMDetector:
    def __init__(self, cp_threshold=85, drift=20, window_size=100, step_size=20, idle_threshold=3, ini_len=50):
        """
        初始化CUSUM检测器
        :param cp_threshold: 变点检测阈值 ，越小检出变点越多，空转检出越少
        :param drift: 最小可检测偏移量 ，越小检出变点越多，空转检出越少
        :param window_size: 滑动窗口大小
        :param step_size: 滑动步长
        :param idle_threshold: 空转阈值，越小空转检出越少
        :param ini_len: 初始化均值数据长度
        """
        self.cp_threshold = cp_threshold
        self.drift = drift
        self.window_size = window_size
        self.step_size = step_size
        self.idle_threshold = idle_threshold
        self.ini_len = ini_len

    def detect_change_points(self, data):
        """
        CUSUM检测变点
        :param data: 输入时序数据
        :return: 变点位置, 正向累积和, 负向累积和
        """
        cum_pos = [0]  # 正向累积和
        cum_neg = [0]  # 负向累积和
        cp_points = []  # 变点位置
        
        # if len(data) < self.ini_len:
        #     raise ValueError(f"数据长度不足，至少需要{self.ini_len}个数据点")
        # mu = np.mean(data[:self.ini_len])
        mu = data[0]
        
        for i, x in enumerate(data):
            s_pos = max(0, cum_pos[-1] + (x - mu - self.drift))
            s_neg = max(0, cum_neg[-1] + (mu - x - self.drift))
            cum_pos.append(s_pos)
            cum_neg.append(s_neg)
            
            if s_pos > self.cp_threshold or s_neg > self.cp_threshold:
                cp_points.append(i)
                if i < 10 or i+10 > len(data):
                    continue
                window = data[i-10:i+10]
                if len(window) > 0:
                    mu = np.mean(window)
        
        return cp_points, cum_pos[1:], cum_neg[1:]

    def detect_stability(self, data, cp_points):
        """
        检测稳定性
        :param data: 输入时序数据
        :param cp_points: 变点位置
        :return: 包含窗口起始点和变点数的DataFrame
        """
        results = []
        
        for start_idx in range(0, len(data) - self.window_size + 1, self.step_size):
            window_current = [i for i in range(start_idx, start_idx + self.window_size)]
            cp_cnt = sum(1 for i in window_current if i in cp_points)
            results.append({
                'start_index': start_idx,
                'cp_cnt': cp_cnt
            })
        
        return pd.DataFrame(results)

    def visualize_results(self, df, pic_path=None, flag=False):
        """
        可视化结果
        :param data: 原始数据
        :param cp_points: 变点位置
        :param stability_df: 稳定性检测结果
        :param idle_threshold: 空转阈值
        :param cum_pos: 正向累积和
        :param cum_neg: 负向累积和
        :param pic_path: 图片保存路径
        """
        data = df['AD'].values
        cp_points, cum_pos, cum_neg = self.detect_change_points(data)
        stability_df = self.detect_stability(data, cp_points)
        stability_df['is_stable'] = stability_df['cp_cnt'] < self.idle_threshold
        plt.figure(figsize=(12, 6))
        
        # 原始数据
        plt.subplot(311)
        plt.plot(data, label='原始数据')
        plt.vlines(cp_points, ymin=min(data), ymax=max(data),
                 colors='r', linestyles='dashed', label='检测到的变点')
        
        max_index = len(data) - 1
        for start in stability_df[stability_df['is_stable']]['start_index']:
            end = min(start + self.window_size, max_index)
            plt.axvspan(start, end, color='green', alpha=0.1, label='Stable Region')
        
        if stability_df['is_stable'].any():
            stable_times = stability_df[stability_df['is_stable']]['start_index']
            plt.scatter(stable_times, data[stable_times],
                      color='red', s=10, label='Stable Start')
        
        plt.title(f"CUSUM 变点检测 (变点阈值={self.cp_threshold}, 空转阈值={self.idle_threshold})")
        
        # 累积和曲线
        plt.subplot(312)
        plt.plot(cum_pos, label='正向累积和')
        plt.plot(cum_neg, label='负向累积和')
        plt.hlines(self.cp_threshold, xmin=0, xmax=len(data),
                 colors='gray', linestyles='dotted', label='阈值')
        plt.legend()
        plt.grid(True)
        
        # 窗口变点数曲线
        plt.subplot(313)
        plt.plot(stability_df['start_index'], stability_df['cp_cnt'], label='变点数')
        plt.hlines(self.idle_threshold, xmin=0, xmax=len(data),
                 colors='gray', linestyles='dotted', label='阈值')
        plt.tight_layout()
        
        if pic_path:
            plt.savefig(pic_path)
        plt.close('all')

        if flag:
            accuracy = stability_df['is_stable'].mean()
            detect_wk = (len(stability_df) - stability_df['is_stable'].sum()) * 1 / 75
            print(f"{pic_path.split('/')[-1]}空转检出率：{accuracy * 100:.2f}%, 检测下料量：{detect_wk:.2f}")
            return accuracy

if __name__ == '__main__':
    # 初始化CUSUM检测器
    detector = CUSUMDetector(cp_threshold=70, drift=10, window_size=10, step_size=2, idle_threshold=3, ini_len=5)
    
    pic_dir = os.path.join(data_path, 'cusum_100ms/')  # 结果存放的目录
    if not os.path.exists(pic_dir):
        os.makedirs(pic_dir)
    
    result_accuracy = {}
    # 读取数据文件夹中的所有Excel文件
    for dfile in os.listdir(data_path):
        if dfile.endswith('.xlsx'):
            excel_path = data_path + dfile
            df = extract_g_column(excel_path)
            df = sample_data(df, 10)  # 10ms采样基础上间隔采样
            df = truncate_d(df, 50, 50).reset_index()

            # 生成可视化结果
            pic_path = pic_dir + dfile.split('.')[0] + '.png'
            accuracy = detector.visualize_results(df, pic_path, flag=True)
            print('save pic to ', pic_path)
            
            # 记录准确率
            result_accuracy[dfile.split('.')[0]] = accuracy
    
    # 保存准确率结果
    result_accuracy_df = pd.DataFrame(result_accuracy.items(), columns=['采样组', '准确率'])
    result_accuracy_df.to_excel(pic_dir + 'result_accuracy.xlsx', index=False)
